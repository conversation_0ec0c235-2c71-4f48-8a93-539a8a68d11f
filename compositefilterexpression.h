#ifndef COMPOSITEFILTEREXPRESSION_H
#define COMPOSITEFILTEREXPRESSION_H

#include "logviewer_global.h"
#include "filterexpression.h"
#include <QList>
#include <memory>
#include <vector>

/**
 * @brief 复合过滤表达式类
 * 
 * 支持AND/OR逻辑组合多个过滤条件
 */
class LOGVIEWER_EXPORT CompositeFilterExpression : public FilterExpression
{
public:
    /**
     * @brief 逻辑操作符枚举
     */
    enum class LogicalOperator
    {
        And,    // 逻辑与
        Or      // 逻辑或
    };

    /**
     * @brief 构造函数
     * @param op 逻辑操作符
     */
    explicit CompositeFilterExpression(LogicalOperator op = LogicalOperator::And);

    /**
     * @brief 析构函数
     */
    ~CompositeFilterExpression() override = default;

    /**
     * @brief 添加子过滤条件
     * @param filter 过滤条件
     */
    void addFilter(std::unique_ptr<FilterExpression> filter);

    /**
     * @brief 移除指定索引的过滤条件
     * @param index 索引
     */
    void removeFilter(int index);

    /**
     * @brief 清除所有过滤条件
     */
    void clearFilters();

    /**
     * @brief 获取过滤条件数量
     */
    int getFilterCount() const;

    /**
     * @brief 获取指定索引的过滤条件
     * @param index 索引
     * @return 过滤条件指针，如果索引无效返回nullptr
     */
    const FilterExpression* getFilter(int index) const;

    /**
     * @brief 设置逻辑操作符
     * @param op 逻辑操作符
     */
    void setLogicalOperator(LogicalOperator op);

    /**
     * @brief 获取逻辑操作符
     */
    LogicalOperator getLogicalOperator() const;

    // FilterExpression接口实现
    bool matches(const LogEntry& entry) const override;
    QString toString() const override;
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject& json);
    std::unique_ptr<FilterExpression> clone() const override;

    /**
     * @brief 逻辑操作符转字符串
     */
    static QString operatorToString(LogicalOperator op);

    /**
     * @brief 字符串转逻辑操作符
     */
    static LogicalOperator stringToOperator(const QString& str);

    /**
     * @brief 验证复合表达式是否有效
     */
    bool isValid() const;

    /**
     * @brief 获取表达式的复杂度（嵌套层数）
     */
    int getComplexity() const;

    /**
     * @brief 优化表达式（移除冗余条件、合并相同类型条件等）
     */
    std::unique_ptr<FilterExpression> optimize() const;

private:
    LogicalOperator _operator;
    std::vector<std::unique_ptr<FilterExpression>> _filters;

    // 私有辅助方法
    bool evaluateAnd(const LogEntry& entry) const;
    bool evaluateOr(const LogEntry& entry) const;
    QString buildToStringRecursive(int depth = 0) const;
    int calculateComplexity(int currentDepth = 0) const;
};

/**
 * @brief 过滤条件构建器
 * 
 * 提供便捷的方法来构建复合过滤表达式
 */
class LOGVIEWER_EXPORT FilterBuilder
{
public:
    FilterBuilder();
    ~FilterBuilder() = default;

    /**
     * @brief 开始一个AND组合
     */
    FilterBuilder& beginAnd();

    /**
     * @brief 开始一个OR组合
     */
    FilterBuilder& beginOr();

    /**
     * @brief 结束当前组合
     */
    FilterBuilder& end();

    /**
     * @brief 添加消息过滤条件
     */
    FilterBuilder& message(const QString& pattern, FilterExpression::MatchType matchType = FilterExpression::MatchType::Contains);

    /**
     * @brief 添加级别过滤条件
     */
    FilterBuilder& level(const QSet<LogEntry::LogLevel>& levels);

    /**
     * @brief 添加时间范围过滤条件
     */
    FilterBuilder& timeRange(const QDateTime& start, const QDateTime& end);

    /**
     * @brief 添加来源过滤条件
     */
    FilterBuilder& source(const QString& pattern, FilterExpression::MatchType matchType = FilterExpression::MatchType::Contains);

    /**
     * @brief 添加自定义过滤条件
     */
    FilterBuilder& custom(std::unique_ptr<FilterExpression> filter);

    /**
     * @brief 构建最终的过滤表达式
     */
    std::unique_ptr<FilterExpression> build();

    /**
     * @brief 重置构建器
     */
    void reset();

    /**
     * @brief 验证当前构建状态是否有效
     */
    bool isValid() const;

private:
    struct BuilderState
    {
        std::unique_ptr<CompositeFilterExpression> composite;
        CompositeFilterExpression::LogicalOperator op;

        // 默认构造函数
        BuilderState() : op(CompositeFilterExpression::LogicalOperator::And) {}

        // 移动构造函数
        BuilderState(BuilderState&& other) noexcept
            : composite(std::move(other.composite)), op(other.op) {}

        // 移动赋值操作符
        BuilderState& operator=(BuilderState&& other) noexcept
        {
            if (this != &other) {
                composite = std::move(other.composite);
                op = other.op;
            }
            return *this;
        }

        // 删除拷贝构造函数和拷贝赋值操作符
        BuilderState(const BuilderState&) = delete;
        BuilderState& operator=(const BuilderState&) = delete;
    };

    std::vector<BuilderState> _stateStack;
    std::unique_ptr<FilterExpression> _rootFilter;

    CompositeFilterExpression* getCurrentComposite();
    void ensureRootComposite();
};

#endif // COMPOSITEFILTEREXPRESSION_H
