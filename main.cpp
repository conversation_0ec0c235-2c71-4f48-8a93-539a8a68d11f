#include <QApplication>
#include "simple_test.h"
#include "streamlined_log4qt_source.h"
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序信息
//    app.setApplicationName("LogViewer Phase1 Test");
//    app.setApplicationVersion("1.0");
//    app.setOrganizationName("LogViewer");

//    qDebug() << "Starting LogViewer Phase1 Test Application";

//    // 测试新的StreamlinedLog4QtSource
//    qDebug() << "\n=== 测试StreamlinedLog4QtSource替换 ===";

//    // 创建并测试新数据源
//    StreamlinedLog4QtSource* streamlinedSource = new StreamlinedLog4QtSource();
//    qDebug() << "✓ StreamlinedLog4QtSource创建成功";
//    qDebug() << "数据源信息:" << streamlinedSource->getSourceInfo();
//    qDebug() << "初始连接状态:" << streamlinedSource->isConnected();

//    // 测试设置日志器名称
//    streamlinedSource->setLoggerName("TestLogger");
//    qDebug() << "设置日志器名称:" << streamlinedSource->getLoggerName();

//    // 测试连接
//    bool connected = streamlinedSource->connectToSource();
//    qDebug() << "连接测试结果:" << (connected ? "✓ 成功" : "✗ 失败");

//    if (connected) {
//        qDebug() << "连接后状态:" << streamlinedSource->isConnected();
//        streamlinedSource->disconnect();
//        qDebug() << "断开连接后状态:" << streamlinedSource->isConnected();
//    }

//    delete streamlinedSource;
//    qDebug() << "✓ StreamlinedLog4QtSource测试完成\n";

    // 创建测试窗口
    SimpleTestWidget window;
    window.show();

    return app.exec();
}
