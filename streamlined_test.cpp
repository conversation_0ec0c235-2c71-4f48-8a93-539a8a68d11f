#include "streamlined_log4qt_source.h"
#include <QDebug>

/**
 * @brief 简单的编译测试
 */
void testStreamlinedSource()
{
    qDebug() << "测试StreamlinedLog4QtSource编译...";
    
    // 创建实例
    StreamlinedLog4QtSource source;
    
    // 测试基本方法
    qDebug() << "数据源信息:" << source.getSourceInfo();
    qDebug() << "是否已连接:" << source.isConnected();
    
    // 测试设置
    source.setLoggerName("TestLogger");
    qDebug() << "日志器名称:" << source.getLoggerName();
    
    qDebug() << "StreamlinedLog4QtSource编译测试通过!";
}
