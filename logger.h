#ifndef LOGGER_H_EDDF664F_FBE7_4E51_85C4_90744AB180EA
#define LOGGER_H_EDDF664F_FBE7_4E51_85C4_90744AB180EA

#include "logger_global.h"
#include "log4qt/logger.h"
#include <QString>

#define TRACE(Text) Logger::getInstance().log(Info, QStringLiteral("root"), Text);
#define DEBUG(Text) Logger::getInstance().log(Debug, QStringLiteral("root"), Text);
#define INFO(Text)  Logger::getInstance().log(Info, QStringLiteral("root"), Text);
#define WARN(Text)  Logger::getInstance().log(Warn, QStringLiteral("root"), Text);
#define ERROR(Text) Logger::getInstance().log(Error, QStringLiteral("root"), Text);
#define FATAL(Text) Logger::getInstance().log(Fatal, QStringLiteral("root"), Text);

#define TRACE_COMM(Text) Logger::getInstance().log(Info, QStringLiteral("commLogger"), Text);
#define DEBUG_COMM(Text) Logger::getInstance().log(Debug, QStringLiteral("commLogger"), Text);
#define INFO_COMM(Text)  Logger::getInstance().log(Info, QStringLiteral("commLogger"), Text);
#define WARN_COMM(Text)  Logger::getInstance().log(Warn, QStringLiteral("commLogger"), Text);
#define ERROR_COMM(Text) Logger::getInstance().log(Error, QStringLiteral("commLogger"), Text);
#define FATAL_COMM(Text) Logger::getInstance().log(Fatal, QStringLiteral("commLogger"), Text);

enum LogLevel
{
    Trace = 64,
    Debug = 96,
    Info = 128,
    Warn = 150,
    Error = 182,
    Fatal = 214,
};

class LOGGER_EXPORT Logger
{
    inline static bool m_isInited{false};
public:
    static Logger getInstance();
    static void init(const QString&logPath);
    void log(LogLevel level, const QString &loggerName, const QString &text);
    Log4Qt::Logger* getRootLogger();

private:
    Logger()=default;
    Logger(const Logger&other)=default;
    static Logger instance;
    static QString m_logPath;
    static Log4Qt::Logger *rootLogger;
};

#endif // LOGGER_H_EDDF664F_FBE7_4E51_85C4_90744AB180EA
