#ifndef SIMPLECONFIGMANAGER_H
#define SIMPLECONFIGMANAGER_H

#include "logviewer_global.h"
#include <QSettings>
#include <QString>

class LOGVIEWER_EXPORT SimpleConfigManager
{
public:
    /**
     * @brief 获取单例实例
     * @return 配置管理器实例
     */
    static SimpleConfigManager& instance();

    // ========== 核心配置项获取方法 ==========
    
    /**
     * @brief 获取默认文件编码
     * @return 编码名称，默认"UTF-8"
     */
    QString getDefaultEncoding() const;

    /**
     * @brief 获取最大文件大小限制（MB）
     * @return 文件大小限制，默认100MB
     */
    int getMaxFileSize() const;

    /**
     * @brief 获取最大日志条目数
     * @return 最大条目数，默认50000
     */
    int getMaxLogEntries() const;

    /**
     * @brief 获取自动滚动设置
     * @return 是否启用自动滚动，默认true
     */
    bool getAutoScroll() const;

    /**
     * @brief 获取是否显示时间戳
     * @return 是否显示时间戳，默认true
     */
    bool getShowTimestamp() const;

    /**
     * @brief 获取窗口几何信息
     * @return 窗口几何信息的字节数组
     */
    QByteArray getWindowGeometry() const;

    /**
     * @brief 获取窗口状态信息
     * @return 窗口状态信息的字节数组
     */
    QByteArray getWindowState() const;

    /**
     * @brief 获取配置文件路径
     * @return 配置文件的完整路径
     */
    QString getConfigFilePath() const;

    // ========== 配置项设置方法 ==========
    
    /**
     * @brief 设置默认文件编码
     * @param encoding 编码名称
     */
    void setDefaultEncoding(const QString& encoding);

    /**
     * @brief 设置最大文件大小限制
     * @param sizeMB 文件大小限制（MB）
     */
    void setMaxFileSize(int sizeMB);

    /**
     * @brief 设置最大日志条目数
     * @param count 最大条目数
     */
    void setMaxLogEntries(int count);

    /**
     * @brief 设置自动滚动
     * @param enabled 是否启用自动滚动
     */
    void setAutoScroll(bool enabled);

    /**
     * @brief 设置是否显示时间戳
     * @param show 是否显示时间戳
     */
    void setShowTimestamp(bool show);

    /**
     * @brief 设置窗口几何信息
     * @param geometry 窗口几何信息
     */
    void setWindowGeometry(const QByteArray& geometry);

    /**
     * @brief 设置窗口状态信息
     * @param state 窗口状态信息
     */
    void setWindowState(const QByteArray& state);

    // ========== 配置持久化方法 ==========
    
    /**
     * @brief 保存配置到文件
     */
    void save();

    /**
     * @brief 从文件加载配置
     */
    void load();

    /**
     * @brief 重置为默认配置
     */
    void resetToDefaults();

    /**
     * @brief 获取所有配置的摘要信息
     * @return 配置摘要字符串
     */
    QString getConfigSummary() const;

private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    SimpleConfigManager();

    /**
     * @brief 私有析构函数
     */
    ~SimpleConfigManager() = default;

    /**
     * @brief 禁用拷贝构造函数
     */
    SimpleConfigManager(const SimpleConfigManager&) = delete;

    /**
     * @brief 禁用赋值操作符
     */
    SimpleConfigManager& operator=(const SimpleConfigManager&) = delete;

    // ========== 辅助方法 ==========
    
    /**
     * @brief 获取字符串配置项
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    QString getString(const QString& key, const QString& defaultValue) const;

    /**
     * @brief 获取整数配置项
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    int getInt(const QString& key, int defaultValue) const;

    /**
     * @brief 获取布尔配置项
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    bool getBool(const QString& key, bool defaultValue) const;

    /**
     * @brief 获取字节数组配置项
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    QByteArray getByteArray(const QString& key, const QByteArray& defaultValue) const;

    /**
     * @brief 设置配置项值
     * @param key 配置键
     * @param value 配置值
     */
    void setValue(const QString& key, const QVariant& value);

private:
    QSettings m_settings;   ///< Qt设置对象

    // ========== 配置键常量 ==========
    static const QString KEY_DEFAULT_ENCODING;
    static const QString KEY_MAX_FILE_SIZE;
    static const QString KEY_MAX_LOG_ENTRIES;
    static const QString KEY_AUTO_SCROLL;
    static const QString KEY_SHOW_TIMESTAMP;
    static const QString KEY_WINDOW_GEOMETRY;
    static const QString KEY_WINDOW_STATE;
};

#endif // SIMPLECONFIGMANAGER_H
