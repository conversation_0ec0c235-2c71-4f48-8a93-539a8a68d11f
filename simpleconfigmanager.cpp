#include "simpleconfigmanager.h"
#include <QApplication>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>

// ========== 配置键常量定义 ==========
const QString SimpleConfigManager::KEY_DEFAULT_ENCODING = "general/defaultEncoding";
const QString SimpleConfigManager::KEY_MAX_FILE_SIZE = "general/maxFileSize";
const QString SimpleConfigManager::KEY_MAX_LOG_ENTRIES = "general/maxLogEntries";
const QString SimpleConfigManager::KEY_AUTO_SCROLL = "ui/autoScroll";
const QString SimpleConfigManager::KEY_SHOW_TIMESTAMP = "ui/showTimestamp";
const QString SimpleConfigManager::KEY_WINDOW_GEOMETRY = "ui/windowGeometry";
const QString SimpleConfigManager::KEY_WINDOW_STATE = "ui/windowState";

SimpleConfigManager::SimpleConfigManager()
    : m_settings(QSettings::IniFormat, QSettings::UserScope, 
                 QApplication::organizationName().isEmpty() ? "LogViewer" : QApplication::organizationName(),
                 QApplication::applicationName().isEmpty() ? "SimpleLogViewer" : QApplication::applicationName())
{
    // 确保配置目录存在
    QString configDir = QFileInfo(m_settings.fileName()).absolutePath();
    QDir().mkpath(configDir);
    
    qDebug() << "SimpleConfigManager initialized, config file:" << m_settings.fileName();
}

SimpleConfigManager& SimpleConfigManager::instance()
{
    static SimpleConfigManager instance;
    return instance;
}

// ========== 配置项获取方法实现 ==========

QString SimpleConfigManager::getDefaultEncoding() const
{
    return getString(KEY_DEFAULT_ENCODING, "UTF-8");
}

int SimpleConfigManager::getMaxFileSize() const
{
    return getInt(KEY_MAX_FILE_SIZE, 100); // 默认100MB
}

int SimpleConfigManager::getMaxLogEntries() const
{
    return getInt(KEY_MAX_LOG_ENTRIES, 20000); // 默认20000条，优化性能
}

bool SimpleConfigManager::getAutoScroll() const
{
    return getBool(KEY_AUTO_SCROLL, true);
}

bool SimpleConfigManager::getShowTimestamp() const
{
    return getBool(KEY_SHOW_TIMESTAMP, true);
}

QByteArray SimpleConfigManager::getWindowGeometry() const
{
    return getByteArray(KEY_WINDOW_GEOMETRY, QByteArray());
}

QByteArray SimpleConfigManager::getWindowState() const
{
    return getByteArray(KEY_WINDOW_STATE, QByteArray());
}

QString SimpleConfigManager::getConfigFilePath() const
{
    return m_settings.fileName();
}

// ========== 配置项设置方法实现 ==========

void SimpleConfigManager::setDefaultEncoding(const QString& encoding)
{
    setValue(KEY_DEFAULT_ENCODING, encoding);
    qDebug() << "Default encoding set to:" << encoding;
}

void SimpleConfigManager::setMaxFileSize(int sizeMB)
{
    if (sizeMB > 0) {
        setValue(KEY_MAX_FILE_SIZE, sizeMB);
        qDebug() << "Max file size set to:" << sizeMB << "MB";
    } else {
        qWarning() << "Invalid max file size:" << sizeMB;
    }
}

void SimpleConfigManager::setMaxLogEntries(int count)
{
    if (count > 0) {
        setValue(KEY_MAX_LOG_ENTRIES, count);
        qDebug() << "Max log entries set to:" << count;
    } else {
        qWarning() << "Invalid max log entries:" << count;
    }
}

void SimpleConfigManager::setAutoScroll(bool enabled)
{
    setValue(KEY_AUTO_SCROLL, enabled);
    qDebug() << "Auto scroll set to:" << enabled;
}

void SimpleConfigManager::setShowTimestamp(bool show)
{
    setValue(KEY_SHOW_TIMESTAMP, show);
    qDebug() << "Show timestamp set to:" << show;
}

void SimpleConfigManager::setWindowGeometry(const QByteArray& geometry)
{
    setValue(KEY_WINDOW_GEOMETRY, geometry);
    qDebug() << "Window geometry saved, size:" << geometry.size() << "bytes";
}

void SimpleConfigManager::setWindowState(const QByteArray& state)
{
    setValue(KEY_WINDOW_STATE, state);
    qDebug() << "Window state saved, size:" << state.size() << "bytes";
}

// ========== 配置持久化方法实现 ==========

void SimpleConfigManager::save()
{
    m_settings.sync();
    
    if (m_settings.status() == QSettings::NoError) {
        qDebug() << "Configuration saved successfully to:" << m_settings.fileName();
    } else {
        qWarning() << "Failed to save configuration, status:" << m_settings.status();
    }
}

void SimpleConfigManager::load()
{
    // QSettings会自动加载，这里主要用于调试输出
    qDebug() << "Configuration loaded from:" << m_settings.fileName();
    qDebug() << "Available keys:" << m_settings.allKeys().size();
}

void SimpleConfigManager::resetToDefaults()
{
    qDebug() << "Resetting configuration to defaults";
    
    m_settings.clear();
    
    // 设置默认值
    setDefaultEncoding("UTF-8");
    setMaxFileSize(100);
    setMaxLogEntries(20000); // 优化性能，减少到2万条
    setAutoScroll(true);
    setShowTimestamp(true);
    
    save();
    
    qDebug() << "Configuration reset completed";
}

QString SimpleConfigManager::getConfigSummary() const
{
    QString summary;
    summary += QString("配置文件: %1\n").arg(getConfigFilePath());
    summary += QString("默认编码: %1\n").arg(getDefaultEncoding());
    summary += QString("最大文件大小: %1 MB\n").arg(getMaxFileSize());
    summary += QString("最大日志条目: %1\n").arg(getMaxLogEntries());
    summary += QString("自动滚动: %1\n").arg(getAutoScroll() ? "是" : "否");
    summary += QString("显示时间戳: %1\n").arg(getShowTimestamp() ? "是" : "否");
    summary += QString("配置项总数: %1").arg(m_settings.allKeys().size());
    
    return summary;
}

// ========== 辅助方法实现 ==========

QString SimpleConfigManager::getString(const QString& key, const QString& defaultValue) const
{
    return m_settings.value(key, defaultValue).toString();
}

int SimpleConfigManager::getInt(const QString& key, int defaultValue) const
{
    bool ok;
    int value = m_settings.value(key, defaultValue).toInt(&ok);
    return ok ? value : defaultValue;
}

bool SimpleConfigManager::getBool(const QString& key, bool defaultValue) const
{
    return m_settings.value(key, defaultValue).toBool();
}

QByteArray SimpleConfigManager::getByteArray(const QString& key, const QByteArray& defaultValue) const
{
    return m_settings.value(key, defaultValue).toByteArray();
}

void SimpleConfigManager::setValue(const QString& key, const QVariant& value)
{
    m_settings.setValue(key, value);
}
