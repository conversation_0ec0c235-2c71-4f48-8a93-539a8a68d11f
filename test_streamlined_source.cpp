#include "streamlined_log4qt_source.h"
#include "streamlined_log_appender.h"
#include <QCoreApplication>
#include <QDebug>
#include <QTimer>

/**
 * @brief 测试精简Log4Qt数据源的基本功能
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 测试StreamlinedLog4QtSource ===";
    
    // 创建数据源
    StreamlinedLog4QtSource source;
    
    // 连接信号
    QObject::connect(&source, &IDataSource::dataReady, [](const QVector<LogEntry>& entries) {
        qDebug() << "接收到" << entries.size() << "条日志数据";
        for (const auto& entry : entries) {
            qDebug() << "  -" << entry.timestamp().toString() 
                     << entry.levelString() 
                     << entry.source() 
                     << entry.message();
        }
    });
    
    QObject::connect(&source, &IDataSource::error, [](const QString& error) {
        qDebug() << "错误:" << error;
    });
    
    QObject::connect(&source, &IDataSource::statusChanged, [](const QString& status) {
        qDebug() << "状态:" << status;
    });
    
    // 测试连接
    qDebug() << "测试连接...";
    bool connected = source.connectToSource();
    qDebug() << "连接结果:" << (connected ? "成功" : "失败");
    
    if (connected) {
        qDebug() << "数据源信息:" << source.getSourceInfo();
        qDebug() << "是否已连接:" << source.isConnected();
        
        // 等待一些数据
        QTimer::singleShot(3000, [&source]() {
            qDebug() << "断开连接...";
            source.disconnect();
        });
        
        QTimer::singleShot(4000, []() {
            qDebug() << "测试完成";
            QCoreApplication::quit();
        });
    } else {
        QTimer::singleShot(1000, []() {
            QCoreApplication::quit();
        });
    }
    
    return app.exec();
}
