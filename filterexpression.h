﻿#ifndef FILTEREXPRESSION_H
#define FILTEREXPRESSION_H

#include "logentry.h"
#include <QString>
#include <QDateTime>
#include <QRegularExpression>
#include <memory>
#include <vector>

class FilterExpression
{
public:
    enum class Operator
    {
        And,
        Or
    };

    enum class MatchType
    {
        Contains,
        Equals,
        StartsWith,
        EndsWith,
        RegExp
    };

    virtual ~FilterExpression()                                                      = default;
    virtual bool                                matches(const LogEntry& entry) const = 0;
    virtual QString                             toString() const                     = 0;
    virtual std::unique_ptr< FilterExpression > clone() const                        = 0;
};

class CompositeFilter : public FilterExpression
{
public:
    CompositeFilter(Operator op = Operator::And);

    void                                addFilter(std::shared_ptr< FilterExpression > filter);
    bool                                matches(const LogEntry& entry) const override;
    QString                             toString() const override;
    std::unique_ptr< FilterExpression > clone() const override;

public:
    Operator                                           _operator;
    std::vector< std::shared_ptr< FilterExpression > > _filters;
};

class MessageFilter : public FilterExpression
{
public:
    MessageFilter(const QString& pattern, MatchType matchType = MatchType::Contains);

    bool                                matches(const LogEntry& entry) const override;
    QString                             toString() const override;
    std::unique_ptr< FilterExpression > clone() const override;

public:
    QString            _pattern;
    MatchType          _matchType;
    QRegularExpression _regex;
    mutable bool       _regexValidated = false;  // 缓存验证状态
};

class LevelFilter : public FilterExpression
{
public:
    LevelFilter(const QSet< LogEntry::LogLevel >& levels);

    bool                                matches(const LogEntry& entry) const override;
    QString                             toString() const override;
    std::unique_ptr< FilterExpression > clone() const override;

public:
    QSet< LogEntry::LogLevel > _levels;
};

class TimeRangeFilter : public FilterExpression
{
public:
    TimeRangeFilter(const QDateTime& start, const QDateTime& end);

    bool                                matches(const LogEntry& entry) const override;
    QString                             toString() const override;
    std::unique_ptr< FilterExpression > clone() const override;

public:
    QDateTime _startTime;
    QDateTime _endTime;
};

class SourceFilter : public FilterExpression
{
public:
    SourceFilter(const QString& source, MatchType matchType = MatchType::Contains);

    bool                                matches(const LogEntry& entry) const override;
    QString                             toString() const override;
    std::unique_ptr< FilterExpression > clone() const override;

public:
    QString            _source;
    MatchType          _matchType;
    QRegularExpression _regex;
    mutable bool       _regexValidated = false;  // 缓存验证状态
};

#endif // FILTEREXPRESSION_H
