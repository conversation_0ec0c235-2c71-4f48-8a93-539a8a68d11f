#ifndef STREAMLINED_LOG4QT_SOURCE_H
#define STREAMLINED_LOG4QT_SOURCE_H

#include "idatasource.h"
#include <QTimer>

// 条件前置声明
#ifdef LOG4QT_AVAILABLE
namespace Log4Qt {
    class Logger;
}
#endif

class StreamlinedLogAppender;

/**
 * @brief 精简高效的Log4Qt数据源
 * 
 * 特点：
 * - 极简初始化：无复杂配置，直接获取rootLogger
 * - 轻量级批量处理：防止高频UI更新
 * - 实时响应：低频场景下100ms内响应
 * - 内存友好：无额外缓存，依赖LogModel管理数据
 * - 代码精简：约150行代码（vs 原来630行）
 */
class LOGVIEWER_EXPORT StreamlinedLog4QtSource : public IDataSource
{
    Q_OBJECT

public:
    explicit StreamlinedLog4QtSource(QObject* parent = nullptr);
    ~StreamlinedLog4QtSource() override;

    // ========== IDataSource接口实现 ==========
    
    bool connectToSource() override;
    QVector<LogEntry> loadData() override;
    void disconnect() override;
    bool isConnected() const override;
    QString getSourceInfo() const override;

    // ========== 配置方法 ==========
    
    void setLoggerName(const QString& loggerName) { m_loggerName = loggerName; }
    QString getLoggerName() const { return m_loggerName; }

private slots:
    /**
     * @brief 接收日志条目
     */
    void onLogReceived(const LogEntry& entry);
    
    /**
     * @brief 刷新批量数据
     */
    void flushBatch();

private:
    // ========== 核心属性 ==========
    QString m_loggerName;
    bool m_connected;
    
    // ========== Log4Qt组件 ==========
#ifdef LOG4QT_AVAILABLE
    Log4Qt::Logger* m_logger;
#else
    void* m_logger; // 占位符
#endif
    StreamlinedLogAppender* m_appender;
    
    // ========== 轻量级批量处理 ==========
    QTimer* m_batchTimer;
    QVector<LogEntry> m_pendingBatch;
    
    // 性能参数
    static const int BATCH_SIZE = 50;        // 批量大小
    static const int BATCH_TIMEOUT = 100;    // 批量超时(ms)
};

#endif // STREAMLINED_LOG4QT_SOURCE_H
