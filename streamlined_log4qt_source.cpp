#include "streamlined_log4qt_source.h"
#include "streamlined_log_appender.h"
#include <QDebug>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/logger.h>
#endif

StreamlinedLog4QtSource::StreamlinedLog4QtSource(QObject* parent)
    : IDataSource(parent)
    , m_loggerName("root")
    , m_connected(false)
    , m_logger(nullptr)
    , m_appender(nullptr)
{
    // 创建轻量级批量处理定时器
    m_batchTimer = new QTimer(this);
    m_batchTimer->setSingleShot(true);
    m_batchTimer->setInterval(BATCH_TIMEOUT);
    connect(m_batchTimer, &QTimer::timeout, this, &StreamlinedLog4QtSource::flushBatch);
    
    qDebug() << "StreamlinedLog4QtSource created";
}

StreamlinedLog4QtSource::~StreamlinedLog4QtSource()
{
    disconnect();
    qDebug() << "StreamlinedLog4QtSource destroyed";
}

bool StreamlinedLog4QtSource::connectToSource()
{
#ifdef LOG4QT_AVAILABLE
    try {
        // 极简初始化：直接获取rootLogger，无复杂配置
        m_logger = Log4Qt::Logger::rootLogger();
        if (!m_logger) {
            emit error("无法获取Log4Qt rootLogger");
            return false;
        }
        
        // 创建精简的Appender
        m_appender = new StreamlinedLogAppender(this);
        connect(m_appender, &StreamlinedLogAppender::logReceived,
                this, &StreamlinedLog4QtSource::onLogReceived);
        
        // 将Appender添加到Logger
        m_appender->setName("StreamlinedLogAppender");
        m_logger->addAppender(m_appender);
        
        m_connected = true;
        emit statusChanged(QString("已连接到Log4Qt日志器: %1").arg(m_loggerName));
        
        qDebug() << "Connected to Log4Qt logger:" << m_loggerName;
        return true;
        
    } catch (const std::exception& e) {
        QString errorMsg = QString("Log4Qt连接失败: %1").arg(e.what());
        emit error(errorMsg);
        return false;
    }
#else
    // 模拟模式：创建测试Appender
    m_logger = reinterpret_cast<void*>(0x1);
    m_appender = new StreamlinedLogAppender(this);
    connect(m_appender, &StreamlinedLogAppender::logReceived,
            this, &StreamlinedLog4QtSource::onLogReceived);
    
    m_connected = true;
    emit statusChanged("已连接到Log4Qt模拟器");
    
    // 生成一些测试数据
    QTimer::singleShot(1000, [this]() {
        for (int i = 0; i < 5; ++i) {
            LogEntry entry(QDateTime::currentDateTime(),
                          static_cast<LogEntry::LogLevel>(i % 5),
                          "TestApp",
                          QString("测试日志消息 #%1").arg(i + 1),
                          "");
            onLogReceived(entry);
        }
    });
    
    qDebug() << "Connected to Log4Qt simulator";
    return true;
#endif
}

QVector<LogEntry> StreamlinedLog4QtSource::loadData()
{
    // 实时数据源不需要主动加载，数据通过信号异步发送
    return QVector<LogEntry>();
}

void StreamlinedLog4QtSource::disconnect()
{
    if (!m_connected) {
        return;
    }
    
    // 发送剩余的批量数据
    flushBatch();
    
#ifdef LOG4QT_AVAILABLE
    if (m_logger && m_appender) {
        Log4Qt::Logger* log4qtLogger = static_cast<Log4Qt::Logger*>(m_logger);
        log4qtLogger->removeAppender(m_appender);
    }
#endif
    
    if (m_appender) {
        m_appender->deleteLater();
        m_appender = nullptr;
    }
    
    m_logger = nullptr;
    m_connected = false;
    
    emit statusChanged("已断开Log4Qt连接");
    qDebug() << "Disconnected from Log4Qt";
}

bool StreamlinedLog4QtSource::isConnected() const
{
    return m_connected;
}

QString StreamlinedLog4QtSource::getSourceInfo() const
{
    return QString("Log4Qt实时日志源 (Logger: %1)").arg(m_loggerName);
}

void StreamlinedLog4QtSource::onLogReceived(const LogEntry& entry)
{
    if (!m_connected) {
        return;
    }
    
    // 轻量级批量处理：防止高频UI更新
    m_pendingBatch.append(entry);
    
    // 达到批量大小立即发送
    if (m_pendingBatch.size() >= BATCH_SIZE) {
        flushBatch();
    }
    // 第一条数据启动定时器
    else if (m_pendingBatch.size() == 1) {
        m_batchTimer->start();
    }
}

void StreamlinedLog4QtSource::flushBatch()
{
    if (m_pendingBatch.isEmpty()) {
        return;
    }
    
    // 发送批量数据
    emit dataReady(m_pendingBatch);
    
    // 清空批量缓冲区
    m_pendingBatch.clear();
    
    // 停止定时器
    if (m_batchTimer->isActive()) {
        m_batchTimer->stop();
    }
}
