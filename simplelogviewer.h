#ifndef SIMPLELOGVIEWER_H
#define SIMPLELOGVIEWER_H

#include "logviewer_global.h"
#include "logentry.h"
#include "logmodel.h"
#include "logsortfilterproxymodel.h"
#include "idatasource.h"

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTableView>
#include <QLineEdit>
#include <QCheckBox>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>
#include <QSplitter>
#include <QProgressBar>
#include <QSet>
#include <memory>

// 前置声明
class SimpleFileDataSource;
class SimpleLog4QtDataSource;

/**
 * @brief 简化的日志查看器主组件
 * 
 * 这是重构后的核心组件，替代了复杂的三层继承结构。
 * 特点：
 * - 单一主组件，无复杂继承
 * - 移除了性能问题组件（SearchIndex、VirtualizedTableView等）
 * - 使用标准QTableView，确保UI响应性
 * - 支持文件查看器和Log4Qt查看器两种模式
 * - 简化的API接口，易于使用和维护
 */
class LOGVIEWER_EXPORT SimpleLogViewer : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 查看器类型枚举
     */
    enum ViewerType {
        FileViewer,     ///< 文件日志查看器
        Log4QtViewer    ///< Log4Qt日志查看器
    };

    /**
     * @brief 构造函数
     * @param type 查看器类型
     * @param parent 父窗口
     */
    explicit SimpleLogViewer(ViewerType type, QWidget* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SimpleLogViewer() override;

    // ========== 核心API方法 ==========
    
    /**
     * @brief 加载日志文件（仅文件查看器模式）
     * @param filePath 文件路径
     * @param encoding 文件编码，默认UTF-8
     * @return 是否成功开始加载
     */
    bool loadFile(const QString& filePath, const QString& encoding = "UTF-8");
    
    /**
     * @brief 连接Log4Qt日志器（仅Log4Qt查看器模式）
     * @param loggerName 日志器名称
     * @return 是否连接成功
     */
    bool connectLog4Qt(const QString& loggerName);
    
    /**
     * @brief 设置文本过滤条件
     * @param filter 过滤文本，空字符串表示不过滤
     */
    void setTextFilter(const QString& filter);
    
    /**
     * @brief 设置日志级别过滤
     * @param levels 允许显示的日志级别集合
     */
    void setLevelFilter(const QSet<LogEntry::LogLevel>& levels);
    
    /**
     * @brief 清除所有日志数据
     */
    void clear();

    // ========== 状态查询方法 ==========
    
    /**
     * @brief 检查是否已连接到数据源
     * @return 连接状态
     */
    bool isConnected() const;
    
    /**
     * @brief 获取当前日志条目数量
     * @return 日志条目数量
     */
    int getLogCount() const;
    
    /**
     * @brief 获取状态信息
     * @return 状态描述字符串
     */
    QString getStatusInfo() const;
    
    /**
     * @brief 获取查看器类型
     * @return 查看器类型
     */
    ViewerType getViewerType() const { return m_type; }

signals:
    /**
     * @brief 数据加载完成信号
     * @param count 加载的日志条目数量
     */
    void dataLoaded(int count);
    
    /**
     * @brief 错误发生信号
     * @param error 错误描述
     */
    void errorOccurred(const QString& error);
    
    /**
     * @brief 状态变化信号
     * @param status 新的状态描述
     */
    void statusChanged(const QString& status);

private slots:
    /**
     * @brief 处理数据源接收到的数据
     * @param entries 日志条目列表
     */
    void onDataReceived(const QVector<LogEntry>& entries);

    /**
     * @brief 处理异步读取开始
     */
    void onAsyncReadingStarted(const QString& filePath, int estimatedLines);

    /**
     * @brief 处理异步读取进度更新
     */
    void onAsyncProgressUpdated(int processedLines, int totalLines, int percentage);

    /**
     * @brief 处理异步数据块就绪
     */
    void onAsyncDataChunkReady(const QVector<LogEntry>& entries, bool isLastChunk);

    /**
     * @brief 处理异步读取完成
     */
    void onAsyncReadingCompleted(int totalEntries, qint64 elapsedMs);
    
    /**
     * @brief 处理搜索文本变化
     */
    void onSearchTextChanged();
    
    /**
     * @brief 处理日志级别过滤变化
     */
    void onLevelFilterChanged();
    
    /**
     * @brief 处理数据源错误
     * @param error 错误描述
     */
    void onDataSourceError(const QString& error);
    
    /**
     * @brief 处理数据源状态变化
     * @param status 状态描述
     */
    void onDataSourceStatusChanged(const QString& status);

    /**
     * @brief 处理表格双击事件
     * @param index 双击的模型索引
     */
    void onTableDoubleClicked(const QModelIndex& index);

private:
    /**
     * @brief 设置UI界面
     */
    void setupUI();
    
    /**
     * @brief 连接信号槽
     */
    void connectSignals();
    
    /**
     * @brief 创建数据源
     */
    void createDataSource();
    
    /**
     * @brief 更新状态显示
     */
    void updateStatus();
    
    /**
     * @brief 更新日志级别过滤器状态
     */
    void updateLevelFilter();
    
    /**
     * @brief 显示错误信息
     * @param error 错误描述
     */
    void showError(const QString& error);

    /**
     * @brief 显示日志详情对话框
     * @param entry 日志条目
     */
    void showLogDetailsDialog(const LogEntry& entry);

private:
    // ========== 核心属性 ==========
    ViewerType m_type;                              ///< 查看器类型
    
    // ========== 数据组件 ==========
    LogModel* m_model;                              ///< 日志数据模型
    LogSortFilterProxyModel* m_proxyModel;          ///< 排序过滤代理模型
    std::unique_ptr<IDataSource> m_dataSource;     ///< 数据源
    
    // ========== UI组件 ==========
    QVBoxLayout* m_mainLayout;                      ///< 主布局
    QSplitter* m_mainSplitter;                      ///< 主分割器
    
    // 控制面板
    QGroupBox* m_controlGroup;                      ///< 控制面板组
    QHBoxLayout* m_controlLayout;                   ///< 控制面板布局
    
    // 搜索组件
    QLineEdit* m_searchEdit;                        ///< 搜索输入框
    QPushButton* m_clearSearchButton;               ///< 清除搜索按钮
    
    // 日志级别过滤组件
    QCheckBox* m_debugCheckBox;                     ///< Debug级别复选框
    QCheckBox* m_infoCheckBox;                      ///< Info级别复选框
    QCheckBox* m_warningCheckBox;                   ///< Warning级别复选框
    QCheckBox* m_errorCheckBox;                     ///< Error级别复选框
    QCheckBox* m_criticalCheckBox;                  ///< Critical级别复选框
    
    // 表格视图
    QTableView* m_tableView;                        ///< 日志表格视图
    
    // 状态显示
    QLabel* m_statusLabel;                          ///< 状态标签
    QProgressBar* m_progressBar;                    ///< 进度条
    
    // ========== 状态属性 ==========
    QString m_currentFilter;                        ///< 当前文本过滤条件
    QSet<LogEntry::LogLevel> m_currentLevelFilter; ///< 当前级别过滤条件
    QString m_currentStatus;                        ///< 当前状态描述
};

#endif // SIMPLELOGVIEWER_H
