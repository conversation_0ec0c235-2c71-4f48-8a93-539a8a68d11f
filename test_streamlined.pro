QT += core
QT -= gui

CONFIG += c++17 console
CONFIG -= app_bundle

TARGET = test_streamlined
TEMPLATE = app

# 定义宏
DEFINES += LOGVIEWER_STATIC_BUILD
DEFINES += LOGVIEWER_LIBRARY_SRC

# Windows特定设置
win32 {
    DEFINES += LOG4QT_AVAILABLE
    INCLUDEPATH += ../include
    LIBS += -L../bin -llog4qt
}

SOURCES += \
    test_streamlined_source.cpp \
    streamlined_log4qt_source.cpp \
    streamlined_log_appender.cpp \
    logentry.cpp

HEADERS += \
    streamlined_log4qt_source.h \
    streamlined_log_appender.h \
    logentry.h \
    idatasource.h \
    logviewer_global.h
