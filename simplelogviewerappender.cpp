#include "simplelogviewerappender.h"
#include <QDateTime>
#include <QDebug>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/level.h>
#include <log4qt/logger.h>
#include <log4qt/ndc.h>
#include <log4qt/mdc.h>
#endif

SimpleLogViewerAppender::SimpleLogViewerAppender(QObject* parent)
#ifdef LOG4QT_AVAILABLE
    : Log4Qt::AppenderSkeleton(parent)
#else
    : QObject(parent)
#endif
    , m_enabled(true)
    , m_totalEntryCount(0)
{
    qDebug() << "SimpleLogViewerAppender created";
}

SimpleLogViewerAppender::~SimpleLogViewerAppender()
{
    qDebug() << "SimpleLogViewerAppender destroyed, total entries processed:" << m_totalEntryCount;
}

void SimpleLogViewerAppender::appendLogEntry(const LogEntry& entry)
{
    if (!m_enabled) {
        return;
    }
    
    m_totalEntryCount++;
    
    // 发出新日志条目信号
    emit newLogEntry(entry);

    // 移除qDebug输出，防止Log4Qt捕获形成递归循环
}

void SimpleLogViewerAppender::setEnabled(bool enabled)
{
    if (m_enabled != enabled) {
        m_enabled = enabled;
        // 移除qDebug输出，防止递归
    }
}

#ifdef LOG4QT_AVAILABLE
// ========== Log4Qt Appender 接口实现 ==========

void SimpleLogViewerAppender::append(const Log4Qt::LoggingEvent& event)
{
    if (!m_enabled) {
        return;
    }

    // 转换Log4Qt事件为LogEntry
    LogEntry entry = convertToLogEntry(event);

    // 更新计数器
    m_totalEntryCount++;

    // 发出新日志条目信号
    emit newLogEntry(entry);

    // 移除qDebug输出，防止Log4Qt捕获形成递归循环
}

bool SimpleLogViewerAppender::requiresLayout() const
{
    // 我们不使用布局，因为我们直接处理LoggingEvent对象
    return false;
}

LogEntry SimpleLogViewerAppender::convertToLogEntry(const Log4Qt::LoggingEvent& event) const
{
    QDateTime timestamp = QDateTime::fromMSecsSinceEpoch(event.timeStamp());

        // 获取日志级别 - Log4Qt 1.5.1 返回的是Level对象
        LogEntry::LogLevel level = convertLogLevel(event.level().toInt());

        // 获取logger名称和消息
        QString source;
        if (event.logger())
            source = event.logger()->name();
        else
            source = "Unknown";

        QString message = event.message();

        // 构建详细信息
        QString details;

        // 获取NDC
        QString ndc = event.ndc();
        if (!ndc.isEmpty()) {
            details += tr("NDC: %1\n").arg(ndc);
        }

        // 获取MDC/属性
        QHash<QString, QString> properties = event.properties();
        if (!properties.isEmpty()) {
            details += tr("属性:\n");
            for (auto it = properties.constBegin(); it != properties.constEnd(); ++it) {
                details += tr("  %1: %2\n").arg(it.key(), it.value());
            }
        }

        // 获取位置信息 - 使用MessageContext
        Log4Qt::MessageContext context = event.context();
        if (context.file && context.line > 0) {
            details += tr("位置: %1:%2 (%3)\n")
                     .arg(QString::fromLatin1(context.file))
                     .arg(context.line)
                     .arg(QString::fromLatin1(context.function));
        }

        // 获取线程名称
        details += tr("线程: %1\n").arg(event.threadName());

        // 改用properties中可能存在的相关信息
        if (properties.contains("fileName")) {
            details += tr("文件: %1\n").arg(properties.value("fileName"));
        }

        if (properties.contains("category")) {
            details += tr("类别: %1\n").arg(properties.value("category"));
        }

        return LogEntry(timestamp, level, source, message, details);

//    // 转换时间戳 - Log4Qt的timeStamp()返回qint64，需要转换为QDateTime
//    QDateTime timestamp = QDateTime::fromMSecsSinceEpoch(event.timeStamp());

//    // 转换日志级别 - 使用简化的方法
//    LogEntry::LogLevel level = LogEntry::LogLevel::Info; // 默认级别
//    int levelInt = 20000; // 默认INFO级别的数值

//    try {
//        Log4Qt::Level log4qtLevel = event.level();
//        levelInt = log4qtLevel.toInt();

//        // 根据Log4Qt级别值进行转换
//        if (levelInt <= 10000) {          // DEBUG级别
//            level = LogEntry::LogLevel::Debug;
//        } else if (levelInt <= 20000) {   // INFO级别
//            level = LogEntry::LogLevel::Info;
//        } else if (levelInt <= 30000) {   // WARN级别
//            level = LogEntry::LogLevel::Warning;
//        } else if (levelInt <= 40000) {   // ERROR级别
//            level = LogEntry::LogLevel::Error;
//        } else {                          // FATAL级别
//            level = LogEntry::LogLevel::Critical;
//        }
//    } catch (...) {
//        // 如果级别转换失败，使用默认级别
//        qWarning() << "Failed to convert Log4Qt level, using default INFO level";
//        levelInt = 20000; // 重置为默认值
//    }

//    // 获取日志器名称作为来源
//    QString source = "unknown";
//    try {
//        if (event.logger()) {
//            source = event.logger()->name();
//        }
//        if (source.isEmpty()) {
//            source = "root";
//        }
//    } catch (...) {
//        source = "root";
//    }

//    // 获取消息内容
//    QString message = "empty message";
//    try {
//        message = event.message();
//        if (message.isEmpty()) {
//            message = "[Empty Log Message]";
//        }
//    } catch (...) {
//        message = "[Failed to get message]";
//    }

//    QString detailsStr = message;

//    return LogEntry(timestamp, level, source, message, detailsStr);
}

QString SimpleLogViewerAppender::logLevelToString(LogEntry::LogLevel level) const
{
    switch (level) {
        case LogEntry::LogLevel::Debug: return "Debug";
        case LogEntry::LogLevel::Info: return "Info";
        case LogEntry::LogLevel::Warning: return "Warning";
        case LogEntry::LogLevel::Error: return "Error";
        case LogEntry::LogLevel::Critical: return "Critical";
        default: return "Unknown";
    }
}

LogEntry::LogLevel SimpleLogViewerAppender::convertLogLevel(int log4qtLevel) const
{
    if (log4qtLevel == Log4Qt::Level::FATAL_INT) {
        return LogEntry::LogLevel::Critical;
    } else if (log4qtLevel == Log4Qt::Level::ERROR_INT) {
        return LogEntry::LogLevel::Error;
    } else if (log4qtLevel == Log4Qt::Level::WARN_INT) {
        return LogEntry::LogLevel::Warning;
    } else if (log4qtLevel == Log4Qt::Level::INFO_INT) {
        return LogEntry::LogLevel::Info;
    } else {
        return LogEntry::LogLevel::Debug;
    }
}

QString SimpleLogViewerAppender::getRelativeTimeString(const QDateTime& timestamp) const
{
    QDateTime now = QDateTime::currentDateTime();
    qint64 secondsAgo = timestamp.secsTo(now);

    if (secondsAgo < 60) {
        return QString("%1 seconds ago").arg(secondsAgo);
    } else if (secondsAgo < 3600) {
        return QString("%1 minutes ago").arg(secondsAgo / 60);
    } else if (secondsAgo < 86400) {
        return QString("%1 hours ago").arg(secondsAgo / 3600);
    } else {
        return QString("%1 days ago").arg(secondsAgo / 86400);
    }
}

#endif // LOG4QT_AVAILABLE
