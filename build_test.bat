@echo off
echo ========================================
echo LogViewer StreamlinedLog4QtSource 编译测试
echo ========================================

echo.
echo 1. 清理旧的构建文件...
if exist Makefile del Makefile
if exist *.o del *.o
if exist moc_*.cpp del moc_*.cpp
if exist ui_*.h del ui_*.h
if exist SimpleLogViewer.exe del SimpleLogViewer.exe

echo.
echo 2. 运行qmake生成Makefile...
qmake LogViewer.pro
if %ERRORLEVEL% neq 0 (
    echo ✗ qmake失败！
    pause
    exit /b 1
)

echo.
echo 3. 编译项目...
mingw32-make
if %ERRORLEVEL% neq 0 (
    echo ✗ 编译失败！
    pause
    exit /b 1
)

echo.
echo ✓ 编译成功！
echo.
echo 4. 运行测试程序...
if exist SimpleLogViewer.exe (
    echo 启动SimpleLogViewer.exe...
    SimpleLogViewer.exe
) else (
    echo ✗ 可执行文件未找到！
)

pause
