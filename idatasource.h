﻿#ifndef IDATASOURCE_H
#define IDATASOURCE_H

#include "logviewer_global.h"
#include "logentry.h"
#include <QObject>
#include <QVector>
#include <QSet>

/**
 * @brief 简化的数据源接口
 *
 * 重构后的数据源接口，从复杂的10+方法简化为3个核心方法：
 * 1. connectToSource() - 连接到数据源
 * 2. loadData() - 同步加载数据
 * 3. disconnect() - 断开连接
 *
 * 移除了复杂的异步回调、过滤器设置、监听机制等，
 * 简化架构，提高可维护性。
 */
class LOGVIEWER_EXPORT IDataSource : public QObject
{
    Q_OBJECT

public:
    explicit IDataSource(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IDataSource() = default;

    // ========== 核心方法（仅3个） ==========

    /**
     * @brief 连接到数据源
     * @return 连接是否成功
     */
    virtual bool connectToSource() = 0;

    /**
     * @brief 同步加载数据
     * @return 加载的日志条目列表
     * @note 这是同步操作，移除了复杂的异步回调机制
     */
    virtual QVector<LogEntry> loadData() = 0;

    /**
     * @brief 断开连接
     */
    virtual void disconnect() = 0;

    // ========== 状态查询方法 ==========

    /**
     * @brief 检查是否已连接
     * @return 连接状态
     */
    virtual bool isConnected() const = 0;

    /**
     * @brief 获取数据源信息
     * @return 数据源描述字符串
     */
    virtual QString getSourceInfo() const = 0;

signals:
    /**
     * @brief 数据准备就绪信号
     * @param entries 日志条目列表
     */
    void dataReady(const QVector<LogEntry>& entries);

    /**
     * @brief 错误发生信号
     * @param message 错误描述
     */
    void error(const QString& message);

    /**
     * @brief 状态变化信号
     * @param status 状态描述
     */
    void statusChanged(const QString& status);
};

#endif // IDATASOURCE_H
