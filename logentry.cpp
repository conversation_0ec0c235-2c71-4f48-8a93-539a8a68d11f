﻿#include "logentry.h"
#include <QJsonObject>
#include <QJsonDocument>

LogEntry::LogEntry()
    : _timestamp(QDateTime::currentDateTime()),
      _level(LogLevel::Info),
      _source(""),
      _message(""),
      _details("")
{
}

LogEntry::LogEntry(const QDateTime &timestamp, LogLevel level, const QString &source,
                   const QString &message, const QString &details)
    : _timestamp(timestamp),
      _level(level),
      _source(source),
      _message(message),
      _details(details)
{
}

QDateTime LogEntry::timestamp() const { return _timestamp; }
LogEntry::LogLevel LogEntry::level() const { return _level; }

QString LogEntry::levelString() const
{
    switch (_level) {
        case LogLevel::Debug: return "Debug";
        case LogLevel::Info: return "Info";
        case LogLevel::Warning: return "Warning";
        case LogLevel::Error: return "Error";
        case LogLevel::Critical: return "Critical";
        default: return "Unknown";
    }
}

QString LogEntry::source() const { return _source; }
QString LogEntry::message() const { return _message; }
QString LogEntry::details() const { return _details; }

bool LogEntry::operator<(const LogEntry &other) const
{
    return _timestamp < other._timestamp;
}

bool LogEntry::matchesFilter(const QString &filter) const
{
    if (filter.isEmpty()) return true;
    Qt::CaseSensitivity cs = Qt::CaseInsensitive;
    return _message.contains(filter, cs) ||
           _source.contains(filter, cs) ||
           _details.contains(filter, cs) ||
           levelString().contains(filter, cs) ||
           _timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz").contains(filter, cs);
}

QVariant LogEntry::toVariant() const
{
    QJsonObject json;
    json["timestamp"] = _timestamp.toString(Qt::ISODate);
    json["level"] = static_cast<int>(_level);
    json["source"] = _source;
    json["message"] = _message;
    json["details"] = _details;
    return QJsonDocument(json).toJson(QJsonDocument::Compact);
}

LogEntry LogEntry::fromVariant(const QVariant &variant)
{
    QJsonDocument doc = QJsonDocument::fromJson(variant.toByteArray());
    QJsonObject json = doc.object();
    return LogEntry(
        QDateTime::fromString(json["timestamp"].toString(), Qt::ISODate),
        static_cast<LogLevel>(json["level"].toInt()),
        json["source"].toString(),
        json["message"].toString(),
        json["details"].toString()
    );
}
