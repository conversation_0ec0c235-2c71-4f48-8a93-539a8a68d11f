#ifndef LOGSORTFILTERPROXYMODEL_H
#define LOGSORTFILTERPROXYMODEL_H

#include "logviewer_global.h"
#include "logentry.h"
#include "filterexpression.h"
#include <QSortFilterProxyModel>
#include <QSet>
#include <QDateTime>
#include <memory>

// 简化版本：定义空的FilterStats结构以保持API兼容性
namespace OptimizedFilterChain {
    struct FilterStats {
        QString name;
        int callCount = 0;
        double averageTime = 0.0;
    };
}

class LOGVIEWER_EXPORT LogSortFilterProxyModel : public QSortFilterProxyModel
{
    Q_OBJECT

public:
    explicit LogSortFilterProxyModel(QObject* parent = nullptr);

    void    setFilterPattern(const QString& pattern);
    QString filterPattern() const;

    void setCaseSensitivity(bool caseSensitive);
    bool caseSensitivity() const;

    void setLevelFilter(LogEntry::LogLevel level, bool show);
    void setLevelFilters(const QSet< LogEntry::LogLevel >& levels);
    bool isLevelVisible(LogEntry::LogLevel level) const;

    void setTimeRangeFilter(const QDateTime& startTime, const QDateTime& endTime);
    void clearTimeRangeFilter();

    void    setSourceFilter(const QString& source);
    QString sourceFilter() const;

    void setFilterExpression(std::unique_ptr< FilterExpression > filter);

    // 获取当前的过滤表达式
    const FilterExpression* getFilterExpression() const { return _filterExpression.get(); }

    // 优化过滤器链支持
    void setOptimizedFilteringEnabled(bool enabled);
    bool isOptimizedFilteringEnabled() const;

    // 获取过滤器性能统计
    QList<OptimizedFilterChain::FilterStats> getFilterStatistics() const;
    void resetFilterStatistics();

protected:
    bool filterAcceptsRow(int sourceRow, const QModelIndex& sourceParent) const override;
    bool lessThan(const QModelIndex& left, const QModelIndex& right) const override;

private:
    QString                             _filterPattern;
    bool                                _caseSensitive;
    QSet< LogEntry::LogLevel >          _levelFilter;
    QDateTime                           _startTime;
    QDateTime                           _endTime;
    bool                                _useTimeRange;
    QString                             _sourceFilter;
    std::unique_ptr< FilterExpression > _filterExpression;

    // 简化版本：移除了优化过滤器链
};

#endif // LOGSORTFILTERPROXYMODEL_H
