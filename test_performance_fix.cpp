#include "simplelog4qtdatasource.h"
#include "logmodel.h"
#include "simplelogviewerappender.h"
#include <QApplication>
#include <QTableView>
#include <QTimer>
#include <QDebug>
#include <QTime>
#include <QVBoxLayout>
#include <QWidget>
#include <QLabel>
#include <QPushButton>

/**
 * @brief 性能测试类，用于验证Log4QT卡死问题的修复效果
 */
class PerformanceTest : public QWidget
{
    Q_OBJECT

public:
    PerformanceTest(QWidget* parent = nullptr) : QWidget(parent)
    {
        setupUI();
        setupDataSource();
        setupModel();
        connectSignals();
    }

private slots:
    void startHighFrequencyTest()
    {
        m_statusLabel->setText("开始高频测试...");
        m_testStartTime = QTime::currentTime();
        m_entryCount = 0;
        
        // 生成25000条测试数据，超过20000的限制
        m_dataSource->generateTestData(25000);
        
        qDebug() << "开始高频测试，生成25000条数据";
    }
    
    void onDataReceived(const QVector<LogEntry>& entries)
    {
        m_entryCount += entries.size();
        
        // 更新状态
        int elapsed = m_testStartTime.msecsTo(QTime::currentTime());
        QString status = QString("已接收: %1 条数据, 耗时: %2 ms, 当前显示: %3 条")
                        .arg(m_entryCount)
                        .arg(elapsed)
                        .arg(m_model->rowCount());
        
        m_statusLabel->setText(status);
        
        // 检查是否完成测试
        if (m_entryCount >= 25000) {
            qDebug() << "测试完成！总耗时:" << elapsed << "ms";
            m_statusLabel->setText(status + " - 测试完成！");
        }
    }
    
    void onMemoryWarning()
    {
        qDebug() << "收到内存警告信号";
        m_statusLabel->setText(m_statusLabel->text() + " [内存清理]");
    }

private:
    void setupUI()
    {
        setWindowTitle("Log4QT性能测试 - 卡死问题修复验证");
        resize(1000, 600);
        
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        // 状态标签
        m_statusLabel = new QLabel("准备就绪", this);
        layout->addWidget(m_statusLabel);
        
        // 测试按钮
        QPushButton* testButton = new QPushButton("开始高频测试 (25000条数据)", this);
        connect(testButton, &QPushButton::clicked, this, &PerformanceTest::startHighFrequencyTest);
        layout->addWidget(testButton);
        
        // 表格视图
        m_tableView = new QTableView(this);
        layout->addWidget(m_tableView);
    }
    
    void setupDataSource()
    {
        m_dataSource = new SimpleLog4QtDataSource("TestLogger", this);
        
        // 连接到数据源
        if (!m_dataSource->connectToSource()) {
            qWarning() << "无法连接到Log4Qt数据源";
        }
    }
    
    void setupModel()
    {
        m_model = new LogModel(this);
        m_tableView->setModel(m_model);
        
        // 优化表格视图性能
        m_tableView->setAlternatingRowColors(true);
        m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        m_tableView->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);
        m_tableView->setHorizontalScrollMode(QAbstractItemView::ScrollPerPixel);
    }
    
    void connectSignals()
    {
        // 连接数据源信号
        connect(m_dataSource, &SimpleLog4QtDataSource::dataReady,
                m_model, &LogModel::addLogEntries);
        
        connect(m_dataSource, &SimpleLog4QtDataSource::dataReady,
                this, &PerformanceTest::onDataReceived);
        
        // 连接内存警告信号
        connect(m_model, &LogModel::memoryWarning,
                this, &PerformanceTest::onMemoryWarning);
    }

private:
    SimpleLog4QtDataSource* m_dataSource;
    LogModel* m_model;
    QTableView* m_tableView;
    QLabel* m_statusLabel;
    QTime m_testStartTime;
    int m_entryCount = 0;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "启动Log4QT性能测试程序";
    
    PerformanceTest test;
    test.show();
    
    return app.exec();
}

#include "test_performance_fix.moc"
