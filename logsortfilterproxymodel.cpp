﻿#include "logsortfilterproxymodel.h"
#include "logmodel.h"
#include <QDebug>

LogSortFilterProxyModel::LogSortFilterProxyModel(QObject* parent)
    : QSortFilterProxyModel(parent), _filterPattern(""), _caseSensitive(false), _useTimeRange(false), _sourceFilter("")
{
    // 初始化级别过滤器，默认显示所有级别
    _levelFilter.insert(LogEntry::LogLevel::Debug);
    _levelFilter.insert(LogEntry::LogLevel::Info);
    _levelFilter.insert(LogEntry::LogLevel::Warning);
    _levelFilter.insert(LogEntry::LogLevel::Error);
    _levelFilter.insert(LogEntry::LogLevel::Critical);

    qDebug() << "LogSortFilterProxyModel initialized (simplified version)";
}

void LogSortFilterProxyModel::setFilterPattern(const QString& pattern)
{
    if (_filterPattern != pattern)
    {
        _filterPattern = pattern;
        invalidateFilter();
    }
}

QString LogSortFilterProxyModel::filterPattern() const { return _filterPattern; }

void LogSortFilterProxyModel::setCaseSensitivity(bool caseSensitive)
{
    if (_caseSensitive != caseSensitive)
    {
        _caseSensitive = caseSensitive;
        invalidateFilter();
    }
}

bool LogSortFilterProxyModel::caseSensitivity() const { return _caseSensitive; }

void LogSortFilterProxyModel::setLevelFilter(LogEntry::LogLevel level, bool show)
{
    bool changed = false;
    if (show)
    {
        if (!_levelFilter.contains(level))
        {
            _levelFilter.insert(level);
            changed = true;
        }
    }
    else
    {
        if (_levelFilter.contains(level))
        {
            _levelFilter.remove(level);
            changed = true;
        }
    }

    if (changed) {
        invalidateFilter();
    }
}

void LogSortFilterProxyModel::setLevelFilters(const QSet< LogEntry::LogLevel >& levels)
{
    if (_levelFilter != levels)
    {
        _levelFilter = levels;
        invalidateFilter();
    }
}

bool LogSortFilterProxyModel::isLevelVisible(LogEntry::LogLevel level) const { return _levelFilter.contains(level); }

void LogSortFilterProxyModel::setTimeRangeFilter(const QDateTime& startTime, const QDateTime& endTime)
{
    _startTime    = startTime;
    _endTime      = endTime;
    _useTimeRange = true;
    invalidateFilter();
}

void LogSortFilterProxyModel::clearTimeRangeFilter()
{
    _useTimeRange = false;
    invalidateFilter();
}

void LogSortFilterProxyModel::setSourceFilter(const QString& source)
{
    if (_sourceFilter != source)
    {
        _sourceFilter = source;
        invalidateFilter();
    }
}

QString LogSortFilterProxyModel::sourceFilter() const { return _sourceFilter; }

void LogSortFilterProxyModel::setFilterExpression(std::unique_ptr<FilterExpression> filter)
{
    _filterExpression = std::move(filter);
    invalidateFilter();
}

bool LogSortFilterProxyModel::filterAcceptsRow(int sourceRow, const QModelIndex& /*sourceParent*/) const
{
    LogModel* logModel = qobject_cast<LogModel*>(sourceModel());
    if (!logModel)
    {
        return false;
    }

    LogEntry entry = logModel->getLogEntry(sourceRow);

    // 简化版本：直接使用基础过滤逻辑
    // 如果有高级过滤表达式，优先使用它
    if (_filterExpression)
    {
        return _filterExpression->matches(entry);
    }

    // 检查日志级别
    if (!_levelFilter.contains(entry.level()))
    {
        return false;
    }

    // 检查时间范围
    if (_useTimeRange)
    {
        if (entry.timestamp() < _startTime || entry.timestamp() > _endTime)
        {
            return false;
        }
    }

    // 检查来源
    if (!_sourceFilter.isEmpty() && !entry.source().contains(_sourceFilter, Qt::CaseInsensitive))
    {
        return false;
    }

    // 检查文本过滤器
    if (!_filterPattern.isEmpty())
    {
        Qt::CaseSensitivity cs = _caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;

        // 检查消息
        if (entry.message().contains(_filterPattern, cs))
        {
            return true;
        }

        // 检查详情
        if (entry.details().contains(_filterPattern, cs))
        {
            return true;
        }

        // 检查来源
        if (entry.source().contains(_filterPattern, cs))
        {
            return true;
        }

        // 检查级别字符串
        if (entry.levelString().contains(_filterPattern, cs))
        {
            return true;
        }

        // 检查时间戳
        if (entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz").contains(_filterPattern, cs))
        {
            return true;
        }

        return false;
    }

    return true;
}

bool LogSortFilterProxyModel::lessThan(const QModelIndex& left, const QModelIndex& right) const
{
    LogModel* logModel = qobject_cast< LogModel* >(sourceModel());
    if (!logModel)
    {
        return QSortFilterProxyModel::lessThan(left, right);
    }

    LogEntry leftEntry  = logModel->getLogEntry(left.row());
    LogEntry rightEntry = logModel->getLogEntry(right.row());

    // 获取实际列
    int column       = left.column();
    int actualColumn = -1;

    int visibleCount = 0;
    for (int i = 0; i < static_cast< int >(LogModel::ColumnCount); ++i)
    {
        if (logModel->isColumnVisible(static_cast< LogModel::Column >(i)))
        {
            if (visibleCount == column)
            {
                actualColumn = i;
                break;
            }
            visibleCount++;
        }
    }

    if (actualColumn == -1)
    {
        return false;
    }

    // 根据列进行排序
    switch (actualColumn)
    {
        case LogModel::TimestampColumn:
            return leftEntry.timestamp() < rightEntry.timestamp();
        case LogModel::LevelColumn:
            return static_cast< int >(leftEntry.level()) < static_cast< int >(rightEntry.level());
        case LogModel::SourceColumn:
            return leftEntry.source() < rightEntry.source();
        case LogModel::MessageColumn:
            return leftEntry.message() < rightEntry.message();
        case LogModel::DetailsColumn:
            return leftEntry.details() < rightEntry.details();
        default:
            return false;
    }
}

void LogSortFilterProxyModel::setOptimizedFilteringEnabled(bool enabled)
{
    // 简化版本：优化过滤已移除，此方法保留以兼容API
    Q_UNUSED(enabled)
    qDebug() << "OptimizedFiltering is not available in simplified version";
}

bool LogSortFilterProxyModel::isOptimizedFilteringEnabled() const
{
    // 简化版本：始终返回false
    return false;
}

QList<OptimizedFilterChain::FilterStats> LogSortFilterProxyModel::getFilterStatistics() const
{
    // 简化版本：返回空列表
    return QList<OptimizedFilterChain::FilterStats>();
}

void LogSortFilterProxyModel::resetFilterStatistics()
{
    // 简化版本：无操作
    qDebug() << "Filter statistics reset (no-op in simplified version)";
}
