#include "compositefilterexpression.h"
#include "filterexpression.h"
#include <QJsonArray>
#include <QJsonObject>
#include <QDebug>

// CompositeFilterExpression 实现

CompositeFilterExpression::CompositeFilterExpression(LogicalOperator op)
    : _operator(op)
{
}

void CompositeFilterExpression::addFilter(std::unique_ptr<FilterExpression> filter)
{
    if (filter) {
        _filters.push_back(std::move(filter));
    }
}

void CompositeFilterExpression::removeFilter(int index)
{
    if (index >= 0 && index < static_cast<int>(_filters.size())) {
        _filters.erase(_filters.begin() + index);
    }
}

void CompositeFilterExpression::clearFilters()
{
    _filters.clear();
}

int CompositeFilterExpression::getFilterCount() const
{
    return static_cast<int>(_filters.size());
}

const FilterExpression* CompositeFilterExpression::getFilter(int index) const
{
    if (index >= 0 && index < static_cast<int>(_filters.size())) {
        return _filters[index].get();
    }
    return nullptr;
}

void CompositeFilterExpression::setLogicalOperator(LogicalOperator op)
{
    _operator = op;
}

CompositeFilterExpression::LogicalOperator CompositeFilterExpression::getLogicalOperator() const
{
    return _operator;
}

bool CompositeFilterExpression::matches(const LogEntry& entry) const
{
    if (_filters.empty()) {
        return true; // 空过滤器匹配所有条目
    }

    switch (_operator) {
        case LogicalOperator::And:
            return evaluateAnd(entry);
        case LogicalOperator::Or:
            return evaluateOr(entry);
        default:
            return false;
    }
}

QString CompositeFilterExpression::toString() const
{
    return buildToStringRecursive();
}

QJsonObject CompositeFilterExpression::toJson() const
{
    QJsonObject json;
    json["type"] = "composite";
    json["operator"] = operatorToString(_operator);
    
    QJsonArray filtersArray;
    for (const auto& filter : _filters) {
        if (filter) {
            // TODO: 实现FilterExpression基类的toJson方法
            // filtersArray.append(filter->toJson());
            QJsonObject filterObj;
            filterObj["type"] = "unknown";
            filtersArray.append(filterObj);
        }
    }
    json["filters"] = filtersArray;
    
    return json;
}

bool CompositeFilterExpression::fromJson(const QJsonObject& json)
{
    if (json["type"].toString() != "composite") {
        return false;
    }
    
    // 解析操作符
    QString opStr = json["operator"].toString();
    _operator = stringToOperator(opStr);
    
    // 清除现有过滤器
    _filters.clear();
    
    // 解析子过滤器
    QJsonArray filtersArray = json["filters"].toArray();
    for (const QJsonValue& value : filtersArray) {
        QJsonObject filterJson = value.toObject();
        
        // 这里需要根据类型创建相应的过滤器
        // 由于这需要工厂模式，暂时简化处理
        // TODO: 实现过滤器工厂
    }
    
    return true;
}

std::unique_ptr<FilterExpression> CompositeFilterExpression::clone() const
{
    auto cloned = std::make_unique<CompositeFilterExpression>(_operator);
    
    for (const auto& filter : _filters) {
        if (filter) {
            cloned->addFilter(filter->clone());
        }
    }
    
    return std::move(cloned);
}

QString CompositeFilterExpression::operatorToString(LogicalOperator op)
{
    switch (op) {
        case LogicalOperator::And: return "AND";
        case LogicalOperator::Or: return "OR";
        default: return "UNKNOWN";
    }
}

CompositeFilterExpression::LogicalOperator CompositeFilterExpression::stringToOperator(const QString& str)
{
    if (str.toUpper() == "AND") {
        return LogicalOperator::And;
    } else if (str.toUpper() == "OR") {
        return LogicalOperator::Or;
    }
    return LogicalOperator::And; // 默认值
}

bool CompositeFilterExpression::isValid() const
{
    if (_filters.empty()) {
        return false; // 复合表达式至少需要一个子过滤器
    }
    
    // 检查所有子过滤器是否有效
    for (const auto& filter : _filters) {
        if (!filter) {
            return false;
        }
        // 如果子过滤器也是复合表达式，递归检查
        if (auto composite = dynamic_cast<const CompositeFilterExpression*>(filter.get())) {
            if (!composite->isValid()) {
                return false;
            }
        }
    }
    
    return true;
}

int CompositeFilterExpression::getComplexity() const
{
    return calculateComplexity();
}

std::unique_ptr<FilterExpression> CompositeFilterExpression::optimize() const
{
    // 简单的优化：如果只有一个子过滤器，直接返回该过滤器
    if (_filters.size() == 1) {
        return _filters[0]->clone();
    }
    
    // 更复杂的优化可以在这里实现
    // 例如：合并相同类型的过滤器、移除冗余条件等
    
    return clone();
}

bool CompositeFilterExpression::evaluateAnd(const LogEntry& entry) const
{
    for (const auto& filter : _filters) {
        if (!filter || !filter->matches(entry)) {
            return false;
        }
    }
    return true;
}

bool CompositeFilterExpression::evaluateOr(const LogEntry& entry) const
{
    for (const auto& filter : _filters) {
        if (filter && filter->matches(entry)) {
            return true;
        }
    }
    return false;
}

QString CompositeFilterExpression::buildToStringRecursive(int depth) const
{
    if (_filters.empty()) {
        return "()";
    }
    
    QString indent = QString("  ").repeated(depth);
    QString result = QString("(\n");
    
    for (int i = 0; i < static_cast<int>(_filters.size()); ++i) {
        if (i > 0) {
            result += QString("%1%2\n").arg(indent).arg(operatorToString(_operator));
        }
        
        result += QString("%1%2").arg(indent);
        
        if (auto composite = dynamic_cast<const CompositeFilterExpression*>(_filters[i].get())) {
            result += composite->buildToStringRecursive(depth + 1);
        } else {
            result += _filters[i]->toString();
        }
        
        if (i < static_cast<int>(_filters.size()) - 1) {
            result += "\n";
        }
    }
    
    result += QString("\n%1)").arg(QString("  ").repeated(qMax(0, depth - 1)));
    return result;
}

int CompositeFilterExpression::calculateComplexity(int currentDepth) const
{
    int maxDepth = currentDepth;
    
    for (const auto& filter : _filters) {
        if (auto composite = dynamic_cast<const CompositeFilterExpression*>(filter.get())) {
            int childDepth = composite->calculateComplexity(currentDepth + 1);
            maxDepth = qMax(maxDepth, childDepth);
        }
    }
    
    return maxDepth;
}

// FilterBuilder 实现

FilterBuilder::FilterBuilder()
{
    reset();
}

FilterBuilder& FilterBuilder::beginAnd()
{
    _stateStack.reserve(_stateStack.size() + 1);
    BuilderState state;
    state.composite = std::make_unique<CompositeFilterExpression>(CompositeFilterExpression::LogicalOperator::And);
    state.op = CompositeFilterExpression::LogicalOperator::And;

    _stateStack.push_back(std::move(state));
    return *this;
}

FilterBuilder& FilterBuilder::beginOr()
{
    _stateStack.reserve(_stateStack.size() + 1);
    BuilderState state;
    state.composite = std::make_unique<CompositeFilterExpression>(CompositeFilterExpression::LogicalOperator::Or);
    state.op = CompositeFilterExpression::LogicalOperator::Or;

    _stateStack.push_back(std::move(state));
    return *this;
}

FilterBuilder& FilterBuilder::end()
{
    if (_stateStack.size() > 1) {
        BuilderState current = std::move(_stateStack.back());
        _stateStack.pop_back();
        BuilderState& parent = _stateStack.back();
        
        parent.composite->addFilter(std::move(current.composite));
    } else if (_stateStack.size() == 1) {
        _rootFilter = std::move(_stateStack.back().composite);
        _stateStack.clear();
    }
    
    return *this;
}

FilterBuilder& FilterBuilder::message(const QString& /*pattern*/, FilterExpression::MatchType /*matchType*/)
{
    ensureRootComposite();
    // TODO: 创建MessageFilter实例
    // getCurrentComposite()->addFilter(std::make_unique<MessageFilter>(pattern, matchType));
    return *this;
}

FilterBuilder& FilterBuilder::level(const QSet<LogEntry::LogLevel>& /*levels*/)
{
    ensureRootComposite();
    // TODO: 创建LevelFilter实例
    // getCurrentComposite()->addFilter(std::make_unique<LevelFilter>(levels));
    return *this;
}

FilterBuilder& FilterBuilder::timeRange(const QDateTime& /*start*/, const QDateTime& /*end*/)
{
    ensureRootComposite();
    // TODO: 创建TimeRangeFilter实例
    // getCurrentComposite()->addFilter(std::make_unique<TimeRangeFilter>(start, end));
    return *this;
}

FilterBuilder& FilterBuilder::source(const QString& /*pattern*/, FilterExpression::MatchType /*matchType*/)
{
    ensureRootComposite();
    // TODO: 创建SourceFilter实例
    // getCurrentComposite()->addFilter(std::make_unique<SourceFilter>(pattern, matchType));
    return *this;
}

FilterBuilder& FilterBuilder::custom(std::unique_ptr<FilterExpression> filter)
{
    ensureRootComposite();
    getCurrentComposite()->addFilter(std::move(filter));
    return *this;
}

std::unique_ptr<FilterExpression> FilterBuilder::build()
{
    // 结束所有未结束的组合
    while (!_stateStack.empty()) {
        end();
    }
    
    return std::move(_rootFilter);
}

void FilterBuilder::reset()
{
    _stateStack.clear();
    _rootFilter.reset();
}

bool FilterBuilder::isValid() const
{
    return _rootFilter != nullptr || !_stateStack.empty();
}

CompositeFilterExpression* FilterBuilder::getCurrentComposite()
{
    if (!_stateStack.empty()) {
        return _stateStack.back().composite.get();
    }
    return nullptr;
}

void FilterBuilder::ensureRootComposite()
{
    if (_stateStack.empty() && !_rootFilter) {
        beginAnd();
    }
}
