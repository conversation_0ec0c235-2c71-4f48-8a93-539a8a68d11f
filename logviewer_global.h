/**
 * @file logviewer_global.h
 * @brief LogViewer全局定义 - 支持应用程序和组件模式
 */

#ifndef LOGVIEWER_GLOBAL_H
#define LOGVIEWER_GLOBAL_H

#include <QtCore/qglobal.h>

// 版本信息
#define LOGVIEWER_VERSION_MAJOR 3
#define LOGVIEWER_VERSION_MINOR 0
#define LOGVIEWER_VERSION_PATCH 0
#define LOGVIEWER_VERSION_STRING "3.0.0"

// 组件版本信息
#define LOGVIEWER_COMPONENT_VERSION_MAJOR 1
#define LOGVIEWER_COMPONENT_VERSION_MINOR 0
#define LOGVIEWER_COMPONENT_VERSION_PATCH 0
#define LOGVIEWER_COMPONENT_VERSION_STRING "1.0.0"

// 导出宏定义
#if defined(LOGVIEWER_STATIC_BUILD)
// 静态构建，不需要导入/导出
#define LOGVIEWER_EXPORT
#elif defined(LOGVIEWER_LIBRARY)
#define LOGVIEWER_EXPORT Q_DECL_EXPORT
#elif defined(LOGVIEWER_LIBRARY_SRC)
#define LOGVIEWER_EXPORT
#elif defined(LOGVIEWER_COMPONENT_BUILD)
#define LOGVIEWER_EXPORT Q_DECL_EXPORT
#else
#define LOGVIEWER_EXPORT Q_DECL_IMPORT
#endif

// 编译配置检查
#if QT_VERSION < QT_VERSION_CHECK(5, 12, 0)
#error "LogViewer requires Qt 5.12 or higher"
#endif

#ifndef QT_NO_DEBUG
#  define LOGVIEWER_DEBUG
#endif

// 调试宏
#ifdef LOGVIEWER_DEBUG
#  include <QDebug>
#  define LOGVIEWER_DEBUG_MSG(msg) qDebug() << "[LogViewer]" << msg
#  define LOGVIEWER_WARNING_MSG(msg) qWarning() << "[LogViewer]" << msg
#  define LOGVIEWER_CRITICAL_MSG(msg) qCritical() << "[LogViewer]" << msg
#else
#  define LOGVIEWER_DEBUG_MSG(msg)
#  define LOGVIEWER_WARNING_MSG(msg)
#  define LOGVIEWER_CRITICAL_MSG(msg)
#endif

// 性能监控宏已移除

// 前向声明 - 组件模式
#ifdef LOGVIEWER_COMPONENT_BUILD
class LogViewerComponent;
class LogViewerComponentConfig;
#endif

// 前向声明 - 通用
class ILogViewer;
class BaseLogViewer;
class FileLogViewer;
class Log4QtLogViewer;
class LogModel;
class LogEntry;
class IDataSource;
class FileDataSource;
class Log4QtRedirectDataSource;

#endif // LOGVIEWER_GLOBAL_H
