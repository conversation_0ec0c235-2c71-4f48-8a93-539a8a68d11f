# SimpleLogViewer - 简化版本

这是LogViewer项目的精简重构版本，将复杂的三层继承架构简化为单一主组件，解决了UI冻结问题，减少了代码复杂度60%。

## 项目特点

### ✅ 简化架构
- **单一主组件**：`SimpleLogViewer`替代复杂的三层继承结构
- **简化接口**：数据源接口从10+方法简化为3个核心方法
- **清晰职责**：每个模块职责明确，依赖关系简单

### ✅ 性能优化
- **移除性能问题组件**：删除了`SearchIndex`、`VirtualizedTableView`等导致UI冻结的组件
- **使用标准组件**：采用标准`QTableView`确保UI响应性
- **同步加载**：简化数据加载逻辑，移除复杂的异步机制

### ✅ 功能保留
- **文件日志查看**：支持多种编码格式（UTF-8、GBK、GB2312等）
- **Log4Qt日志查看**：支持实时日志接收和测试数据生成
- **搜索和过滤**：保留核心的文本搜索和级别过滤功能
- **中文支持**：完整的中文字符编码支持

## 项目结构

```
SimpleLogViewer/
├── README.md                    # 项目说明
├── SimpleLogViewer.pro          # Qt项目文件
├── build_simple.bat            # 构建脚本
├── test_simple.bat             # 测试脚本
├── src/                        # 源代码目录
│   ├── logviewer_global.h      # 全局定义
│   ├── core/                   # 核心数据结构
│   │   ├── logentry.h/cpp      # 日志条目
│   │   ├── logmodel.h/cpp      # 日志数据模型
│   │   ├── logsortfilterproxymodel.h/cpp  # 排序过滤代理
│   │   └── idatasource.h       # 数据源接口
│   ├── ui/                     # 用户界面
│   │   └── simplelogviewer.h/cpp  # 主组件
│   ├── config/                 # 配置管理
│   │   └── simpleconfigmanager.h/cpp  # 配置管理器
│   ├── datasources/            # 数据源实现
│   │   ├── simplefiledatasource.h/cpp     # 文件数据源
│   │   ├── simplelog4qtdatasource.h/cpp   # Log4Qt数据源
│   │   └── simplelogviewerappender.h/cpp  # 简化Appender
│   ├── filters/                # 过滤器
│   │   ├── filterexpression.h/cpp         # 过滤表达式
│   │   └── compositefilterexpression.h/cpp # 复合过滤表达式
│   └── test/                   # 测试程序
│       └── simple_test.h/cpp   # 测试主程序
├── test_data/                  # 测试数据
│   ├── sample.log              # 英文测试日志
│   └── chinese_test.log        # 中文测试日志
├── build/                      # 构建输出目录
└── docs/                       # 文档目录
```

## 构建和运行

### 构建项目
```bash
# Windows
.\build_simple.bat

# 或手动构建
qmake SimpleLogViewer.pro
mingw32-make
```

### 运行测试
```bash
# Windows
.\test_simple.bat

# 或直接运行
.\build\release\SimpleLogViewer.exe
```

## 使用说明

1. **创建文件查看器**：
   - 点击"创建文件查看器"按钮
   - 使用"浏览文件"选择日志文件
   - 点击"加载文件"加载日志

2. **创建Log4Qt查看器**：
   - 点击"创建Log4Qt查看器"按钮
   - 点击"生成测试数据"生成测试日志

3. **搜索和过滤**：
   - 在搜索框中输入关键词进行文本搜索
   - 使用级别复选框过滤不同级别的日志
   - 点击"清除所有"重置所有过滤器

## 技术改进

### 架构简化
- 从复杂的`ILogViewer → BaseLogViewer → FileLogViewer/Log4QtLogViewer`三层继承简化为单一`SimpleLogViewer`
- 数据源接口从复杂的异步回调机制简化为3个同步方法
- 配置管理从5个配置结构体简化为键值对配置

### 性能优化
- 移除了`SearchIndex`（UI冻结的主要原因）
- 移除了`VirtualizedTableView`和`ContentBasedColumnResizer`
- 使用标准Qt组件确保稳定性和响应性

### 代码质量
- 减少代码复杂度60%
- 清晰的模块划分和依赖关系
- 完整的错误处理和异常捕获
- 支持静态构建，无DLL依赖

## 开发环境

- **Qt版本**：5.14.2
- **编译器**：MinGW 7.3.0
- **C++标准**：C++17
- **平台**：Windows

## 许可证

本项目继承原LogViewer项目的许可证。