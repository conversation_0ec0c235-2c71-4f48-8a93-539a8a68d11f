﻿#include "filterexpression.h"
#include <QDebug>

// CompositeFilter
CompositeFilter::CompositeFilter(Operator op)
    : _operator(op)
{
}

void CompositeFilter::addFilter(std::shared_ptr<FilterExpression> filter)
{
    _filters.push_back(filter);
}

bool CompositeFilter::matches(const LogEntry &entry) const
{
    if (_filters.empty()) return true;
    
    if (_operator == Operator::And) {
        for (const auto &filter : _filters) {
            if (!filter->matches(entry)) return false;
        }
        return true;
    } else { // Operator::Or
        for (const auto &filter : _filters) {
            if (filter->matches(entry)) return true;
        }
        return false;
    }
}

QString CompositeFilter::toString() const
{
    QStringList parts;
    for (const auto &filter : _filters) {
        parts.append(filter->toString());
    }
    QString op = (_operator == Operator::And) ? "AND" : "OR";
    return QString("(%1)").arg(parts.join(" " + op + " "));
}

std::unique_ptr<FilterExpression> CompositeFilter::clone() const
{
    auto result = std::make_unique<CompositeFilter>(_operator);
    for (const auto &filter : _filters) {
        result->addFilter(filter->clone());
    }
    return result;
}

// MessageFilter
MessageFilter::MessageFilter(const QString &pattern, MatchType matchType)
    : _pattern(pattern),
      _matchType(matchType)
{
    qDebug() << "创建MessageFilter - 模式:" << pattern << "类型:" << static_cast<int>(matchType);

    if (matchType == MatchType::RegExp) {
        // 检查模式是否为空或无效
        if (pattern.isEmpty()) {
            qWarning() << "正则表达式模式为空，回退到包含匹配";
            _matchType = MatchType::Contains;
        } else {
            _regex = QRegularExpression(pattern, QRegularExpression::CaseInsensitiveOption);
            if (!_regex.isValid()) {
                qWarning() << "无效的正则表达式模式:" << pattern << "错误:" << _regex.errorString();
                // 如果正则表达式无效，回退到包含匹配
                _matchType = MatchType::Contains;
            } else {
                qDebug() << "成功创建正则表达式:" << pattern;
            }
        }
    }
}

bool MessageFilter::matches(const LogEntry &entry) const
{
    QString message = entry.message();
    switch (_matchType) {
        case MatchType::Contains: return message.contains(_pattern, Qt::CaseInsensitive);
        case MatchType::Equals: return message.compare(_pattern, Qt::CaseInsensitive) == 0;
        case MatchType::StartsWith: return message.startsWith(_pattern, Qt::CaseInsensitive);
        case MatchType::EndsWith: return message.endsWith(_pattern, Qt::CaseInsensitive);
        case MatchType::RegExp:
            // 只在第一次验证时输出调试信息
            if (!_regexValidated) {
                _regexValidated = true;
                if (!_regex.isValid()) {
                    qWarning() << "MessageFilter: 无效正则表达式，回退到包含匹配。模式:" << _pattern << "错误:" << _regex.errorString();
                }
            }

            if (_regex.isValid()) {
                return _regex.match(message).hasMatch();
            } else {
                // 如果正则表达式无效，回退到包含匹配
                return message.contains(_pattern, Qt::CaseInsensitive);
            }
        default: return false;
    }
}

QString MessageFilter::toString() const
{
    QString typeStr;
    switch (_matchType) {
        case MatchType::Contains: typeStr = "contains"; break;
        case MatchType::Equals: typeStr = "equals"; break;
        case MatchType::StartsWith: typeStr = "startsWith"; break;
        case MatchType::EndsWith: typeStr = "endsWith"; break;
        case MatchType::RegExp: typeStr = "regex"; break;
        default: typeStr = "unknown";
    }
    return QString("message %1 '%2'").arg(typeStr).arg(_pattern);
}

std::unique_ptr<FilterExpression> MessageFilter::clone() const
{
    return std::make_unique<MessageFilter>(_pattern, _matchType);
}

// LevelFilter
LevelFilter::LevelFilter(const QSet<LogEntry::LogLevel> &levels)
    : _levels(levels)
{
}

bool LevelFilter::matches(const LogEntry &entry) const
{
    return _levels.contains(entry.level());
}

QString LevelFilter::toString() const
{
    QStringList levelNames;
    for (LogEntry::LogLevel level : _levels) {
        switch (level) {
            case LogEntry::LogLevel::Debug: levelNames.append("debug"); break;
            case LogEntry::LogLevel::Info: levelNames.append("info"); break;
            case LogEntry::LogLevel::Warning: levelNames.append("warning"); break;
            case LogEntry::LogLevel::Error: levelNames.append("error"); break;
            case LogEntry::LogLevel::Critical: levelNames.append("critical"); break;
        }
    }
    return QString("level in [%1]").arg(levelNames.join(", "));
}

std::unique_ptr<FilterExpression> LevelFilter::clone() const
{
    return std::make_unique<LevelFilter>(_levels);
}

// TimeRangeFilter
TimeRangeFilter::TimeRangeFilter(const QDateTime &start, const QDateTime &end)
    : _startTime(start),
      _endTime(end)
{
}

bool TimeRangeFilter::matches(const LogEntry &entry) const
{
    QDateTime timestamp = entry.timestamp();
    bool afterStart = _startTime.isNull() || timestamp >= _startTime;
    bool beforeEnd = _endTime.isNull() || timestamp <= _endTime;
    return afterStart && beforeEnd;
}

QString TimeRangeFilter::toString() const
{
    QString startStr = _startTime.isNull() ? "start" : _startTime.toString("yyyy-MM-dd hh:mm:ss");
    QString endStr = _endTime.isNull() ? "end" : _endTime.toString("yyyy-MM-dd hh:mm:ss");
    return QString("time from %1 to %2").arg(startStr).arg(endStr);
}

std::unique_ptr<FilterExpression> TimeRangeFilter::clone() const
{
    return std::make_unique<TimeRangeFilter>(_startTime, _endTime);
}

// SourceFilter
SourceFilter::SourceFilter(const QString &source, MatchType matchType)
    : _source(source),
      _matchType(matchType)
{
    qDebug() << "创建SourceFilter - 模式:" << source << "类型:" << static_cast<int>(matchType);

    if (matchType == MatchType::RegExp) {
        // 检查模式是否为空或无效
        if (source.isEmpty()) {
            qWarning() << "正则表达式模式为空，回退到包含匹配";
            _matchType = MatchType::Contains;
        } else {
            _regex = QRegularExpression(source, QRegularExpression::CaseInsensitiveOption);
            if (!_regex.isValid()) {
                qWarning() << "无效的正则表达式模式:" << source << "错误:" << _regex.errorString();
                // 如果正则表达式无效，回退到包含匹配
                _matchType = MatchType::Contains;
            } else {
                qDebug() << "成功创建正则表达式:" << source;
            }
        }
    }
}

bool SourceFilter::matches(const LogEntry &entry) const
{
    QString source = entry.source();
    switch (_matchType) {
        case MatchType::Contains: return source.contains(_source, Qt::CaseInsensitive);
        case MatchType::Equals: return source.compare(_source, Qt::CaseInsensitive) == 0;
        case MatchType::StartsWith: return source.startsWith(_source, Qt::CaseInsensitive);
        case MatchType::EndsWith: return source.endsWith(_source, Qt::CaseInsensitive);
        case MatchType::RegExp:
            // 只在第一次验证时输出调试信息
            if (!_regexValidated) {
                _regexValidated = true;
                if (!_regex.isValid()) {
                    qWarning() << "SourceFilter: 无效正则表达式，回退到包含匹配。模式:" << _source << "错误:" << _regex.errorString();
                }
            }

            if (_regex.isValid()) {
                return _regex.match(source).hasMatch();
            } else {
                // 如果正则表达式无效，回退到包含匹配
                return source.contains(_source, Qt::CaseInsensitive);
            }
        default: return false;
    }
}

QString SourceFilter::toString() const
{
    QString typeStr;
    switch (_matchType) {
        case MatchType::Contains: typeStr = "contains"; break;
        case MatchType::Equals: typeStr = "equals"; break;
        case MatchType::StartsWith: typeStr = "startsWith"; break;
        case MatchType::EndsWith: typeStr = "endsWith"; break;
        case MatchType::RegExp: typeStr = "regex"; break;
        default: typeStr = "unknown";
    }
    return QString("source %1 '%2'").arg(typeStr).arg(_source);
}

std::unique_ptr<FilterExpression> SourceFilter::clone() const
{
    return std::make_unique<SourceFilter>(_source, _matchType);
}
